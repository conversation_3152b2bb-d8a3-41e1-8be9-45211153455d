<?php
namespace app\home\controller;

use app\home\controller\Common;

use app\services\MailService;
use think\facade\Validate;
use think\facade\Cache;
use think\facade\Db;

class Captcha extends Common
{
    // 验证码有效期（分钟）
    const CAPTCHA_EXPIRE = 15;

    /**
     * 发送验证码邮件
     */
    public function send()
    {
        $email = request()->post('email');
        $type = request()->post('type');

        // 验证邮箱格式
        $validate = Validate::rule('email', 'email');
        if (!$validate->check(['email' => $email])) {
            $this->error('Please enter a valid email address');
        }

        if($type == "register"){
            //注册时，验证邮箱是否已注册
            $where = [
                ["email", "=", $email],
                ["status", "=", 1],
            ];
            $userData = Db::name("User")->where($where)->find();
            if($userData){
                $this->error('Email already exists');
            }
        } elseif($type == "forgot"){
            //忘记密码时，验证邮箱是否存在
            $where = [
                ["email", "=", $email],
                ["status", "=", 1],
            ];
            $userData = Db::name("User")->where($where)->find();
            if(!$userData){
                $this->error('Email does not exist or account has been disabled');
            }
        }

        // 发送频率限制（60秒内只能发送一次）
        $lastSendTime = Cache::get('mail_last_send_' . $email);
        if ($lastSendTime && time() - $lastSendTime < 60) {
            $this->error('Sending too frequently, please try again later');
        }

        $captcha = mt_rand(********, ********);
        $data = [
            'captcha' => $captcha,
            'expire' => self::CAPTCHA_EXPIRE,
            'username' => strstr($email, '@', true)
        ];

        $result = MailService::sendEmail("send-captcha", $email, $data);
        if($result === true){
            // 发送成功，设置验证码缓存
            Cache::set('mail_captcha_' . $email, $captcha, self::CAPTCHA_EXPIRE * 60);
            Cache::set('mail_last_send_' . $email, time());

            $this->success('Verification code sent successfully');
        } else {
            $this->error('Verification code sending failed: ' . $result);
        }
    }

    /**
     * 验证验证码
     */
    public function verify()
    {
        $email = request()->post('email');
        $captcha = request()->post('captcha');

        if (empty($email) || empty($captcha)) {
            $this->error('Incomplete parameters');
        }

        $cacheCaptcha = Cache::get('mail_captcha_' . $email);
        if (!empty($cacheCaptcha) && $cacheCaptcha == $captcha) {
            // 验证成功后删除验证码
            Cache::set('mail_captcha_' . $email, null);

            $this->success('The verification code is correct');
        }

        $this->error('Verification code error or expired');
    }
}