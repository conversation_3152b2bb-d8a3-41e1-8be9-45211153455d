<?php /*a:1:{s:63:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\login\forgot.html";i:1754271478;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Reset Password - OpenDEL™</title>
    <link rel="stylesheet" href="/static/home/<USER>/style.css">
    </link>
</head>
<style>
    ::-webkit-input-placeholder {
        color: #ddd;
    }

    ::-moz-placeholder {
        color: #ddd;
    }
</style>

<body>
    <div class="w-full md:min-h-lh  md:grid md:grid-cols-2">
        <div class="hidden md:block pr-20 md:h-lvh sticky top-0">
            <img src="/static/home/<USER>/backgrounds/login_bj.jpg" class="object-cover w-full h-full" alt="">
        </div>
        <div class="flex flex-col w-11/12 mx-auto py-5 pb-10 md:p-0">
            <header class="mb-7 md:fixed md:left-9 md:top-9">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="Login" class="h-8 md:hidden" />
                    <img src="/static/home/<USER>/logo-login.png" alt="Login" class="hidden md:block md:w-[9.4rem]" />
                </a>
            </header>

            <main class="md:flex md:pl-10 md:pr-36">
                <form action="" method="post" id="forgotForm" class="md:w-full md:pt-28 md:pb-10">
                    <h1 class="text-xl Roboto_Bold mb-8
                    md:text-[2.5rem] md:mb-16">
                        Reset Password to OpenDELClub
                    </h1>
                    <div class="flex flex-col gap-y-4 text-sm md:text-xl">
                        <div class="input-item">
                            <label for="email" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Email
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="email" name="email" id="email" placeholder="Please input your registered email" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/youxiang.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12
                                md:h-[3.75rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center] md:pr-32
                                " autocomplete="off" />
                                <button type="button" class="text-white bg-[#f08411] rounded-md top-1/2 -translate-y-1/2 absolute right-2 px-2 py-1.5 cursor-pointer
                                    md:right-4 md:text-base" id="sendOtpBtn">Send OTP</button>
                            </div>
                            <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                            md:text-base
                            ">
                                <img src="/static/home/<USER>/icons/tis.png" alt=""> Please enter a valid email address
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="captcha" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Captcha
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="text" name="captcha" id="captcha"
                                    placeholder="Please enter email verification code" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/yanzhengma.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12
                                    md:h-[3.75rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]" autocomplete="off" />
                            </div>
                            <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                            md:text-base
                            ">
                                <img src="/static/home/<USER>/icons/tis.png" alt=""> Please enter the verification information
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="password" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> New Password
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="password" name="password" id="password"
                                    placeholder="Enter your new password" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                    md:h-[3.75rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]" autocomplete="off" />
                                <button type="button" class="bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                    md:bg-size-auto" id="passwordEye">
                                </button>
                            </div>
                            <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                            md:text-base
                            ">
                                <img src="/static/home/<USER>/icons/tis.png" alt=""> 8-16
                                characters must contain both digits and letters
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="password_confirm" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Confirm New Password
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="password" name="password_confirm" id="password_confirm"
                                    placeholder="Confirm your new password" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                    md:h-[3.75rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]" autocomplete="off" />
                                <button type="button" class="bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                    md:bg-size-auto" id="passwordConfirmEye">
                                </button>
                            </div>
                            <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                            md:text-base
                            ">
                                <img src="/static/home/<USER>/icons/tis.png" alt=""> Passwords do not match
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="w-full h-[3.125rem] bg-[#2e5a8a] text-white rounded-md Roboto_Bold text-base mt-6
                    md:h-[3.75rem] md:text-xl md:mt-8">
                        Reset Password
                    </button>

                    <div class="text-center mt-4 md:mt-6">
                        <span class="text-gray-600 md:text-lg">Remember your password? </span>
                        <a href="<?php echo url('home/login/index'); ?>" class="text-[#f08411] hover:underline md:text-lg">Sign In</a>
                    </div>
                </form>
            </main>

            </div>
        </div>
    </div>

    <script src="/static/home/<USER>/jquery.min.js"></script>
    <script src="/static/layer/layer.js"></script>
    <script>
        $(document).ready(function() {
            // 密码显示/隐藏切换
            $('#passwordEye').click(function() {
                var passwordInput = $('#password');
                var eyeButton = $(this);

                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    eyeButton.removeClass('bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)]').addClass('bg-[url(/static/home/<USER>/icons/yanjing_xianshi_o.png)]');
                } else {
                    passwordInput.attr('type', 'password');
                    eyeButton.removeClass('bg-[url(/static/home/<USER>/icons/yanjing_xianshi_o.png)]').addClass('bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)]');
                }
            });

            // 确认密码显示/隐藏切换
            $('#passwordConfirmEye').click(function() {
                var passwordInput = $('#password_confirm');
                var eyeButton = $(this);

                if (passwordInput.attr('type') === 'password') {
                    passwordInput.attr('type', 'text');
                    eyeButton.removeClass('bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)]').addClass('bg-[url(/static/home/<USER>/icons/yanjing_xianshi_o.png)]');
                } else {
                    passwordInput.attr('type', 'password');
                    eyeButton.removeClass('bg-[url(/static/home/<USER>/icons/yanjing_xianshi_o.png)]').addClass('bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)]');
                }
            });

            // 发送验证码
            $('#sendOtpBtn').click(function() {
                var $btn = $(this);
                var $emailInput = $('#email');

                if (!$emailInput.val()) {
                    layer.msg('Please enter your email address', { icon: 2 });
                    return;
                }

                // 验证邮箱格式
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test($emailInput.val())) {
                    layer.msg('Please enter a valid email address', { icon: 2 });
                    return;
                }

                // 禁用发送按钮
                $btn.prop('disabled', true).addClass('opacity-50');

                // 发送AJAX请求
                $.ajax({
                    url: '/send_captcha',
                    type: 'POST',
                    data: {email: $emailInput.val(), type: 'forgot'},
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            //发送成功
                            layer.msg(data.msg, { icon: 1, time: 2000 });
                        } else {
                            //发送失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $btn.prop('disabled', false).removeClass('opacity-50');
                    }
                });
            });

            // 表单提交
            $('#forgotForm').submit(function(e) {
                e.preventDefault();

                var formData = $(this).serialize();

                $.ajax({
                    url: '/login/forgot',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            layer.msg(data.msg, { icon: 1, time: 2000 }, function() {
                                window.location.href = '/login';
                            });
                        } else {
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    }
                });
            });
        });
    </script>
</body>

</html>
