<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;
use think\Request;

class Index extends Common
{
    public function index()
    {
        $this->setTdk(1);

        $banner = Db::name("Banner")->order("sort asc")->select();

        //最新新闻
        $news = Db::name('News')->alias("n")
                    ->field("n.id, n.title,n.seo_url, n.content, n.content, n.publish_date, c.name, c.color, c.backgroud_color")
                    ->leftjoin("News_category c", "n.category_id=c.id")
                    ->order("n.publish_date desc")
                    ->limit(4)
                    ->select();

        //推荐产品
        $product = Db::name("Product")->where("is_recommend", 1)->find();
        $product['services'] = Db::name("Product_relation")->alias("pr")
        ->field("s.*")
        ->leftjoin("Service s", "pr.related_id=s.id")
        ->where(["product_id"=>$product['id'], "type"=>1])
        ->order("pr.sort asc")
        ->select();

        $faq = Db::name("Faq")->limit(6)->select();

        $resource = Db::name("Resource_category")
            ->limit(2)
            ->select()
            ->each(function ($item) {
                $item['files'] = Db::name("Resource")
                    ->where("category_id", $item['id'])
                    ->limit(5)
                    ->select()
                    ->each(function ($i) {
                        $i['file_type'] = getFileType($i['file_name']);

                        return $i;
                    });

                return $item;
            });


        //最新帖子
        $post_news = Db::name("News")
            ->order("publish_date desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $item['item_url'] = 'news'; // 添加类型标识
                $item['post_type'] = 1;  //非问答

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 1,
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        $where = [
            ["status", "=", 1],  //审核通过
        ];
        $post = Db::name("Forum_posts")
            ->where($where)
            ->order("create_time desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $item['item_url'] = 'post'; // 添加类型标识
                return $item;
            });
        // 合并两个数据集
        $all_post = array_merge($post_news->toArray(), $post->toArray());
        // 按时间倒序排序
        usort($all_post, function ($a, $b) {
            return strtotime($b['create_time']) - strtotime($a['create_time']);
        });
        $all_post = array_chunk($all_post, 6);

        return view("", [
            "banner" => $banner,
            "news" => $news,
            "product" => $product,
            "faq" => $faq,
            "resource" => $resource,
            "all_post" => $all_post,
        ]);
    }
    private function getUserInfo($user_id) {
        $where = [
            "id" => $user_id,
            "status" => 1,
        ];
        $user = Db::name("User")->where($where)->find();
        $user['role_name'] = $user['role_id']?Db::name("Roles")->where("id", $user['role_id'])->value("name"):"";

        return $user;
    }


    //提交反馈
    public function submitFeedback(Request $request){
        $data = $request->post();

        if (empty($data['name']) || empty($data['email']) || empty($data['phone']) || empty($data['company']) || empty($data['agree'])) {
            $this->error('Please fill in all required fields and agree to the terms');
        }

        $data['ip'] = $request->ip();
        $data['create_time'] = date("Y-m-d H:i:s");
        $s = Db::name("Feedback")->strict(false)->insertGetId($data);
        if ($s) {
            $sender = Db::name("User")->field("id, email")->where("email", $data['email'])->find();

            //给产品经理发送站内消息和邮件
            $where = [
                "role_id" => 2,  //产品经理角色
                "status" => 1
            ];
            $product_manager = Db::name("User")->where($where)->column("id, email");
            if(!empty($product_manager)) {
                foreach ($product_manager as $val){
                    //发送站内消息
                    $message_data = [
                        "user_id" => $val['id'],  //产品经理id
                        "sender_id" => !empty($sender)?$sender['id']:0,
                        "content" => "You have an online message for consultation：".$data['feedback'],
                        "main_id" => $s,  //留言id
                        "type" => 2,
                        "create_time" => date('Y-m-d H:i:s')
                    ];
                    Db::name("User_message")->insert($message_data);

                    //发送通知邮件
                    $email_data = [
                        "system_name" => config('app.system_name'),
                        "name" => $data['name'],
                        "phone" => $data['phone'],
                        "email" => $data['email'],
                        "company" => $data['company'],
                        "create_time" => $data['create_time'],
                        "message_content" => $data['feedback'],
                        "login_link" => config('app.site_url')."admin",
                    ];
                    \app\services\MailService::sendEmail("send-feedback", $val['email'], $email_data);
                }
            }

            $this->success('Message sent successfully!');
        } else {
            $this->error("Failed to send message！");
        }
    }

}
