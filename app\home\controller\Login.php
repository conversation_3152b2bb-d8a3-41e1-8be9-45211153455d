<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;
use think\facade\Cache;

use app\validate\Users as UsersValidate;
use think\exception\ValidateException;

class Login extends Common
{
    //登录
    public function index()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            //验证数据
            try {
                validate(UsersValidate::class)->scene('front-login')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            //验证邮箱验证码
            $cacheCaptcha = Cache::get('mail_captcha_' . $data['email']);
            if (!empty($cacheCaptcha) && $cacheCaptcha == $data['captcha']) {
                // 验证成功后删除验证码
                // Cache::set('mail_captcha_' . $data['email'], null);
            } else {
                $this->error("Verification code error or expired!");
            }

            //获取用户信息
            $where = [
                ["email", "=", $data['email']],
                ["status", "=", 1],
            ];
            $userData = Db::name("User")->where($where)->find();
            if (empty($userData)) {
                return $this->error('User does not exist or has been disabled!');
            }

            // 依据用户表中的salt字段生成hash明码
            $password = generateHashPassword($data['password'], $userData['salt']);
            if (strcasecmp($password, $userData['password']) != 0) {
                return $this->error('Password error, please re-enter!');
            }

            session('userId', $userData['id']);
            session('userEmail', $userData['email']);

            //登录成功后删除验证码
            Cache::set('mail_captcha_' . $data['email'], null);

            return $this->success('Login success', "/");
        }

        if(session("userId")) {
            return redirect('/user');
        }

        return view();
    }

    //注册
    public function register()
    {
       if ($this->request->isPost()) {
            $data = $this->request->post();

            //验证数据
            try {
                validate(UsersValidate::class)->scene('front-register')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            //验证邮箱验证码
            $cacheCaptcha = Cache::get('mail_captcha_' . $data['email']);
            if (!empty($cacheCaptcha) && $cacheCaptcha == $data['captcha']) {
                // 验证成功后删除验证码
                // Cache::set('mail_captcha_' . $data['email'], null);
            } else {
                $this->error("Verification code error or expired!");
            }

            // 生成salt
            $data['salt'] = generateSalt();
            // 明码进行加盐hash解决
            $data['password'] = generateHashPassword($data['password'], $data['salt']);

            $data['role_id'] = 4; //默认初级用户
            $data['create_time'] = date("Y-m-d H:i:s");
            $userId = Db::name("User")->strict(false)->insertGetId($data);
            if ($userId) {
                session('userId', $userId);
                session('userEmail', $data['email']);

                //注册成功后删除验证码
                Cache::set('mail_captcha_' . $data['email'], null);

                $this->success('registered success', "/");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }

        if(session("userId")) {
            return redirect('/');
        }

        $country = getCountry();

        return view("", [
            'country' => $country
        ]);
    }

    //忘记密码
    public function forgot() {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            //验证数据
            try {
                validate(UsersValidate::class)->scene('front-forgot')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            //验证邮箱验证码
            $cacheCaptcha = Cache::get('mail_captcha_' . $data['email']);
            if (!empty($cacheCaptcha) && $cacheCaptcha == $data['captcha']) {
                // 验证成功后删除验证码
                Cache::set('mail_captcha_' . $data['email'], null);
            } else {
                $this->error("Verification code error or expired!");
            }

            //检查用户是否存在
            $where = [
                ["email", "=", $data['email']],
                ["status", "=", 1],
            ];
            $userData = Db::name("User")->where($where)->find();
            if (empty($userData)) {
                return $this->error('User does not exist or has been disabled!');
            }

            // 生成新的salt
            $data['salt'] = generateSalt();
            // 明码进行加盐hash解决
            $data['password'] = generateHashPassword($data['password'], $data['salt']);

            // 更新密码
            $updateData = [
                'password' => $data['password'],
                'salt' => $data['salt'],
                'update_time' => date("Y-m-d H:i:s")
            ];

            $result = Db::name("User")->where("id", $userData['id'])->update($updateData);
            if ($result) {
                return $this->success('Password reset successfully! Please login with your new password.');
            } else {
                $this->error("Password reset failed, please try again!");
            }
        }

        if(session("userId")) {
            return redirect('/user');
        }

        return view();
    }

    //退出登录
    public function logout()
    {
        //清除前台登录信息
        session('userId', null);
        session('userEmail', null);

        return redirect('/');
    }

}
